import React from 'react';
import { ColumnGroupType, Tooltip } from '@blmcp/ui';
import { cloneDeep, remove } from 'lodash';
import SortDownIcon from '@/assets/lego/sort-down.svg';
import SortUpIcon from '@/assets/lego/sort-up.svg';
import SortNoIcon from '@/assets/lego/sort-no.svg';
import { ResData, SortType } from '../../components/types';
import { calculateColumnWidth } from '../../utils/css';

type ValueType = string | number | undefined;
const tableCellWidth = 160;

// 提取 React 节点中的文本内容，用于比较
const extractTitleText = (title: any): string => {
  if (typeof title === 'string') {
    return title;
  }
  // 如果是 React 节点，提取其中的文本
  if (
    title &&
    typeof title === 'object' &&
    title.props &&
    title.props.children
  ) {
    const children = title.props.children;
    if (children && typeof children === 'object' && children.props) {
      return children.props.children || '';
    }
    return children || '';
  }
  return '';
};

// 带 tooltip 的表头节点
const createTitleWithTooltip = (
  title: string,
  customTooltipText?: string, // 自定义 tooltip 文案
): React.ReactNode => {
  return (
    <Tooltip
      title={customTooltipText}
      placement="top"
      overlayStyle={{ zIndex: 1000 }}
    >
      {title}
    </Tooltip>
  );
};

// 根据列名查找对应的 tooltip 文案
const findTooltipByTitle = (title: string, measureInfo?: any[]): string => {
  if (!measureInfo || !Array.isArray(measureInfo)) {
    return '';
  }

  const measure = measureInfo.find((item) => item.title === title);
  if (measure && measure.tips) {
    return measure.tips;
  }
  return '';
};

interface TableRenderData {
  columns: ColumnGroupType<Record<string, ValueType>>[];
  dataSource: Record<string, ValueType>;
  total?: number;
  pageNum?: number;
  pageSize?: number;
  contrastLength: number;
}

interface SortItemType {
  index: number;
  sortOrder: number;
}

const iconStyle = {
  display: 'flex',
  alignItems: 'center',
  height: '100%',
  marginLeft: '2px',
};
const getDefaultSortOrder = (index: number, sort: SortItemType) =>
  index === sort.index - 1
    ? String(sort.sortOrder) === SortType.ASC
      ? 'ascend'
      : 'descend'
    : undefined;

const getSortIcon = ({ sortOrder }: { sortOrder: string | null }) => {
  if (sortOrder === 'ascend') {
    return (
      <div style={iconStyle}>
        {' '}
        <SortUpIcon />{' '}
      </div>
    );
  } else if (sortOrder === 'descend') {
    return (
      <div style={iconStyle}>
        {' '}
        <SortDownIcon />
      </div>
    );
  } else {
    return (
      <div style={iconStyle}>
        {' '}
        <SortNoIcon />
      </div>
    );
  }
};

const createColumns = (
  cells: string[],
  key: string,
  selectGroupColumns: string[],
  index: number,
  sort: SortItemType,
  measureInfo?: any[], // 添加 measureInfo 参数
) => {
  if (cells.length === 1) {
    /**
     * 数据列处理 - 根据是否为最上层决定是否显示 tooltip
     * 场景: cells = ["销售额"]
     */
    const newKey = `${key}-${cells[0]}`;
    selectGroupColumns.push(newKey);
    return [
      {
        title: cells[0],
        dataIndex: newKey,
        sortOrder: getDefaultSortOrder(index, sort),
        index: index++,
        key: newKey,
        width: calculateColumnWidth(cells[0]), // 数据列
        ellipsis: true,
        sorter: true,
        sortIcon: getSortIcon,
      },
    ];
  } else {
    /**
     * 对比分组的中间层表头 - 使用自定义节点显示 tooltip
     * 场景: cells = ["2023年", "Q1", "销售额"]，这是"2023年"或"Q1"这样的分组层
     */
    return [
      {
        title: cells[0], // 对比分组表头不显示 tooltip
        children: createColumns(
          cells.slice(1),
          `${key}-${cells[0]}`,
          selectGroupColumns,
          index,
          sort,
          measureInfo,
        ),
      },
    ];
  }
};

const getGroupColumns = (
  columns: { cells: string[] }[],
  selectGroupColumns: string[],
  index: number,
  sort: SortItemType,
  measureInfo?: any[], // 添加 measureInfo 参数，用于获取 tooltip
): {
  groupColumns: ColumnGroupType<Record<string, ValueType>>[];
  duplicateIndex: number[];
} => {
  let measureIndex = 0;
  // 分组表头
  const groupColumns: ColumnGroupType<Record<string, ValueType>>[] = [];
  const duplicateIndex: number[] = [];
  columns?.forEach((col, colIndex: number) => {
    const cells = col.cells;

    // 简单列处理 - 没有对比列，仅有维度和数值，没有分组层级
    if (cells.length <= 1) {
      // 用于分组合并，title为节点，通过 extractTitleText 提取文本进行比较
      const itemGroup = groupColumns.filter((groupCols) => {
        return extractTitleText(groupCols.title) === cells[0];
      });
      // 条件1.1: 首次出现该列名，创建新列
      if (!itemGroup.length) {
        measureIndex = measureIndex + 1;
        selectGroupColumns.push(cells[0]);
        groupColumns.push({
          title: createTitleWithTooltip(
            cells[0],
            measureInfo[measureIndex - 1]?.tips,
          ),
          sortOrder: getDefaultSortOrder(index, sort),
          dataIndex: cells[0],
          index: index++,
          key: cells[0],
          width: calculateColumnWidth(cells[0]), // 数据列
          ellipsis: true,
          sorter: true,
          sortIcon: getSortIcon,
        });
      } else {
        // 重复列名处理，避免 dataIndex 冲突，给新列添加序号前缀
        const accuKey = itemGroup.length + 1;
        const newKey = `${accuKey}_${cells[0]}`;

        selectGroupColumns.push(newKey);
        const customTooltip = findTooltipByTitle(cells[0], measureInfo);
        groupColumns.push({
          title: createTitleWithTooltip(cells[0], customTooltip), // 重复列表头 - 添加 tooltip
          sortOrder: getDefaultSortOrder(index, sort),
          dataIndex: newKey,
          index: index++,
          key: newKey,
          width: calculateColumnWidth(cells[0]), // 数据列
          ellipsis: true,
          sorter: true,
          sortIcon: getSortIcon,
        });
      }
    } else {
      // 条件2: 复杂分组列处理 - 有对比列，存在多层级结构
      let currentGroupColumns = groupColumns;
      let current = 0;
      console.log()
      while (current < cells.length) {
        const currentValue = cells[current];
        // 使用 extractTitleText 提取文本进行比较
        const itemGroup = currentGroupColumns.find((groupCols) => {
          const first = extractTitleText(groupCols.title);
          const second = currentValue;
          return first === second;
        });
        console.log(
          col,
          '------走进来看看累加了几次222',
          current,
          cells,
          'currentValue',
          itemGroup,
          currentGroupColumns,
        );
        // 在当前层级找到了相同名称的分组，则向下一级找
        if (itemGroup) {
          console.log(
            '走进来看看累加了几次1111',
            current,
            cells,
            'currentValue',
            currentValue,
            measureIndex,
            'itemGroup',
            itemGroup,
          );
          current++;
          currentGroupColumns = itemGroup.children;
          // 找到最后一个节点，还是重复的，
          if (current === cells.length) {
            duplicateIndex.push(colIndex);
          }
        } else {
          console.log(
            '=====走进来看看累加了几次3333',
            current,
            cells,
            'currentValue',
            measureIndex,
          );
          /**
           * 在当前层级没有找到相同名称的分组，需要创建新的分组或列
           * 根据是否为最后一层决定创建数据列还是分组列
           */
          // 最后一层 - 创建数据列（对比分组场景，不显示 tooltip）
          if (current === cells.length - 1) {
            const newKey = cells.join('-');
            currentGroupColumns.push({
              title: cells[current], // 对比分组的最底层数据列不显示 tooltip
              dataIndex: newKey,
              sortOrder: getDefaultSortOrder(index, sort),
              index: index++,
              key: newKey,
              width: calculateColumnWidth(cells[current]), // 数据列
              ellipsis: true,
              sorter: true,
              sortIcon: getSortIcon,
            });

            selectGroupColumns.push(newKey);
          } else {
            /**
             * 条件2.2.2: 中间层 - 创建分组列
             * 场景: current = 0 或 1, cells = ["2023年", "Q1", "销售额"]，在"2023年"或"Q1"这一层
             * 说明: 创建分组表头，包含子列
             */
            if (current === 0) {
              measureIndex = measureIndex + 1;
              /**
               * 条件*******: 从根层级开始创建
               * 场景: current = 0，从"2023年"开始创建整个分组结构
               * 说明: 使用 createColumns 递归创建完整的子结构
               */
              currentGroupColumns.push({
                title: createTitleWithTooltip(
                  cells[current],
                  measureInfo[measureIndex - 1]?.tips,
                ),
                children: createColumns(
                  cells.slice(1),
                  cells.join('-'),
                  selectGroupColumns,
                  index++,
                  sort,
                  measureInfo,
                ),
              });
            } else {
              /**
               * 条件*******: 从中间层级开始创建
               * 场景: current = 1，从"Q1"开始创建剩余的结构
               * 说明: 使用 createColumns 创建从当前位置到末尾的结构
               */
              currentGroupColumns.push(
                ...createColumns(
                  cells.slice(current),
                  cells.slice(0, current).join('-'),
                  selectGroupColumns,
                  index++,
                  sort,
                  measureInfo,
                ),
              );
            }
          }
          /**
           * 重置循环变量，跳出当前列的处理
           * 说明: 已经处理完当前列，准备处理下一列
           */
          currentGroupColumns = null;
          current = 0;
          break;
        }
      }
    }
  });

  return { groupColumns, duplicateIndex };
};

export const handleData = (
  data: ResData<{
    columns: { cells: string[] }[];
    rows: { cells: string[] }[];
    values: number[][];
  }>,
): TableRenderData => {
  const { dimensionInfo, contrastInfo, values: vals } = data ?? {};
  let indexSort = 0;
  const values = vals?.values;
  const rows = vals?.rows;
  const valColumns = vals?.columns;
  const sort = data?.sort?.[0] ?? {};

  // 列表头
  const columns: ColumnGroupType<Record<string, ValueType>>[] = [];
  // 表头列
  const selectColumns: string[] = [];
  // 分组列
  const selectGroupColumns: string[] = [];
  const dataSource: Record<string, ValueType> = [];

  // 构建行 - 使用 dimensionInfo 中的 tooltip 字段
  dimensionInfo?.forEach((dim, index) => {
    const sortOrder = getDefaultSortOrder(index, sort);
    // 服务直接返回tips,直接使用
    columns.push({
      title: createTitleWithTooltip(
        dim.title,
        dim?.tips, // 使用从 config 中解析的 tooltip 文案
      ),
      dataIndex: dim.key,
      index: indexSort++,
      key: dim.key,
      width: calculateColumnWidth(dim.title),
      ellipsis: true,
      sortOrder,
      sorter: true,
      sortIcon: getSortIcon,
    });
    selectColumns.push(dim.key);
  });
  rows?.forEach((row, index) => {
    row.cells.forEach((val, idx) => {
      if (dataSource[idx]) {
        dataSource[idx][selectColumns[index]] = val;
      } else {
        const itemLine: Record<string, string> = {};
        itemLine[selectColumns[index]] = val;
        dataSource.push(itemLine);
      }
    });
  });

  // 构建对比分组列
  const {
    groupColumns,
    duplicateIndex,
  }: {
    groupColumns: ColumnGroupType<Record<string, ValueType>>[];
    duplicateIndex: number[];
  } = getGroupColumns(
    valColumns,
    selectGroupColumns,
    indexSort,
    sort,
    data?.measureInfo,
  );

  const finalValue = values.map((itemValue) => {
    const value = cloneDeep(itemValue);

    remove(value, (item, index) => {
      return duplicateIndex.includes(index);
    });
    return value;
  });
  // 构建列
  finalValue?.forEach((row, idx) => {
    const itemRow = row;
    const itemLine: Record<string, string | number> = dataSource?.[idx] ?? {};

    selectGroupColumns.forEach((key, index) => {
      itemLine[key] = itemRow[index];
    });
  });

  return {
    columns: [...columns, ...groupColumns],
    dataSource,
    total: data?.totalSize,
    pageNum: data?.pageNo,
    pageSize: data?.pageSize,
    contrastLength: contrastInfo?.length ?? 0,
  };
};
