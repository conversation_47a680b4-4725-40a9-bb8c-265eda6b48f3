/**
 * 列表组件
 */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ListFilter as ListFilterView } from '../../components/Filter/ListFilter';
import useComponent from '../../hooks/useComponent';
import { DataSetConfig } from '../../type';
import './index.less';
import { getFilters } from '../../api';
import relationCenterExp from '../module/RelationCenter';
import isMobile from '../../utils/isMobile';
import { globalCache } from '@/pages/lego/utils/cache';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';

interface ListFilterProps {
  dataSetConfig?: DataSetConfig;
  __id?: string; // 预览模式
  componentId?: string;
  __designMode?: string; // 编辑模式
  disabled?: boolean; // 禁用
  defaultValue?: string[];
  uuid: string;
  options: any;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          ignoreSetDefaultComputeType: true,
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateDisabled: true, // 不能选日期
      indexDisabled: true,
    },
  },
  defaultValue: {
    componentName: 'ListFilterDefaultValueSetter',
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};
// 逻辑层
export const ListFilter = ({
  dataSetConfig,
  __id,
  componentId,
  __designMode,
  disabled,
  defaultValue,
  uuid,
  reportId,
  options: _options,
}: ListFilterProps) => {
  const relationCenter = relationCenterExp(uuid);
  const { dimensionInfo } = dataSetConfig ?? {};
  const filterId = __id ?? componentId ?? '';
  const editable =
    __designMode === 'design' || reportStore.get(uuid).publishStatus === 0;
  const filterRef = useRef();
  // defaultValue 为组件筛选传入的，优先级更高
  const defaultVal =
    (defaultValue?.length && defaultValue) ||
    dimensionInfo?.[0]?.defaultVal?.stringVal ||
    [];

  const [meta, setMeta] = useComponent(filterId);
  const [options, setOption] = useState<{ label: string; value: string }[]>([]);

  const getData = useCallback(async () => {
    const params = {
      // 报表ID 必填
      reportId,
      elementId: meta.elementId, // 组件服务ID 必填
      componentType: 4, // 必填 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
      requestDataType: 1,
      publishStatus: editable ? 0 : 1,
      filterInfo: [],
    };
    // 缓存 key
    const cacheKey = [filterId, meta.elementId, JSON.stringify(params || {})];

    const res = await getFilters(params);
    setMeta({ queryState: true }, true);

    // const options: { label: string; value: string }[] = [];
    const optionsMap = new Map<string, { label: string; value: string }>();
    ((res.data?.values as []) || []).forEach((item) => {
      const data: any = Object.values(item);
      optionsMap.set(data[0], {
        label: data[0],
        value: data[0],
      });
    });
    const options = Array.from(optionsMap.values());

    // 缓存
    globalCache.add(cacheKey, options);

    setOption(options);

    // 设置默认值
    const defaultVal =
      (defaultValue?.length && defaultValue) ||
      res.data.dimensionInfo[0]?.defaultVal?.stringVal ||
      [];
    filterRef.current?.setValue?.(defaultVal);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editable, meta.elementId]);

  const handleFilterData = useCallback(
    async (fieldValue: string) => {
      const params = {
        // 报表ID 必填
        reportId,
        elementId: meta.elementId, // 组件服务ID 必填
        componentType: 4, // 必填 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
        requestDataType: 1,
        publishStatus: editable ? 0 : 1,
        filterInfo: [
          {
            columnId: dimensionInfo?.[0]?.columnId,
            key: dimensionInfo?.[0]?.key,
            dataType: dimensionInfo?.[0]?.dataType,
            symbol: dimensionInfo?.[0]?.dataType === 1 ? 'IN' : 'LIKE',
            fieldValue: [fieldValue],
          },
        ],
      };

      return getFilters(params)
        .then((res) => {
          const optionsMap = new Map<
            string,
            { label: string; value: string }
          >();
          ((res.data?.values as []) || []).forEach((item: any) => {
            const data: any = Object.values(item);
            optionsMap.set(data[0], {
              label: data[0],
              value: data[0],
            });
          });
          return Array.from(optionsMap.values());
        })
        .catch((e) => {
          if (e === '请勿重复请求') {
            return Promise.reject({
              loading: true,
            });
          } else {
            return [];
          }
        });
    },
    [editable, meta.elementId, dimensionInfo],
  );

  const FilterKey = useMemo(() => {
    return dataSetConfig?.dimensionInfo?.[0]?.key;
  }, [dataSetConfig]);

  const selects = useMemo(() => {
    return dimensionInfo?.[0]?.defaultVal?.stringVal || [];
  }, [dimensionInfo]);

  useEffect(() => {
    if (!selects.length) {
      setMeta({ componentTempProps: { selects: [] } }, true);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selects]);

  useEffect(() => {
    setMeta({ queryState: false }, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [FilterKey /** 别配置 setMeta */]);

  useEffect(() => {
    setMeta(
      {
        query(...arg) {
          // 当改变维度时触发, 设置默认值
          const defaultValue = dimensionInfo?.[0]?.defaultVal?.stringVal || [];
          filterRef.current?.setValue?.(defaultValue);
          getData(...arg);
        },
      },
      true,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getData, dimensionInfo /** 别配置 setMeta */]);

  // 初始化判断是否可查询，设计模式下 拖拽会通过 meta.query 进行查询
  useEffect(() => {
    if (dimensionInfo?.[0] && meta.elementId) {
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    console.log('列表筛选调用', defaultVal);
    if (dimensionInfo?.[0] && meta.elementId && meta.isLinkDataSet !== false) {
      console.log('列表筛选调用11111', dimensionInfo, meta);
      relationCenter.registerFilter(`list_filter_${filterId}`);
      // getData();
      setTimeout(() => {
        relationCenter.readyFilter(`list_filter_${filterId}`);
      }, 0);
    }
    // 卸载的同时，删除写入的查询参数
  }, [dimensionInfo, filterId, getData, meta.elementId, meta.isLinkDataSet]);

  if (dimensionInfo?.[0] && meta.elementId !== undefined) {
    return (
      <ListFilterView
        ref={filterRef}
        dimensionInfo={dimensionInfo?.[0]}
        filterId={filterId}
        elementId={meta.elementId}
        editable={editable}
        options={_options || options}
        disabled={disabled}
        defaultValue={defaultVal}
        handleFilterData={handleFilterData}
        uuid={uuid}
      />
    );
  } else {
    if (isMobile()) {
      return <div className="lego-filter-wrap mobile">请选择列表维度</div>;
    }
    return <div className="lego-filter-wrap">请选择列表维度</div>;
  }
};

ListFilter.displayName = '列表筛选器';
