import { ColumnsType, Tooltip } from '@blmcp/ui';
import { ResData, SortType } from '../../components/types';
import { DimensionInfo } from '../../api/types';
import { formatValue } from '../module/utils';
import isMobile from '../../utils/isMobile';

const getSuffix = (
  title: string,
  key: string,
  index: number,
  firstData: any,
  type: string,
): string => {
  const titleField = `${title}_${type}${index}`;
  const keyField = `${key}_${type}${index}`;
  if (firstData[titleField] !== undefined) return titleField;
  if (firstData[keyField] !== undefined) return keyField;
  return '';
};

// 动态计算列表头宽度，内容宽度根据table宽度自适应
const calculateColumnWidth = (title: string) => {
  const titleWidth = title?.length * 14 + 32 + 12 + 10 || 0;
  return Math.max(titleWidth, 100);
};

// 查找对应tips字段
const findFieldAfterMerge = (dimension, measure, keyValue) => {
  console.log(
    dimension,
    'dimensionInfodimension[index]-----33333',
    measure,
    'keyValue',
    keyValue,
  );
  const dimensionArray = Array.isArray(dimension) ? dimension : [];
  const measureArray = Array.isArray(measure) ? measure : [];
  const mergedArray = [...dimensionArray, ...measureArray];
  console.log(
    mergedArray,
    'dimensionInfodimension[index]-----4444',
    dimensionArray,
    'keyValue',
    measureArray,
    mergedArray,
  );
  const foundItem = mergedArray?.find((item) => item?.key === keyValue);
  console.log(mergedArray, 'mergedArray---', foundItem, keyValue);
  return foundItem?.tips;
};

export const handleData =
  (dimensionInfo: DimensionInfo[]) => (data: ResData) => {
    const dataSource = data?.values ?? [];
    const dimension = data?.dimensionInfo ?? [];
    const measure = data?.measureInfo;
    const sort = data?.sort?.[0] ?? {};

    const getIndex = (columnId: number, dims: any[]) => {
      const index = dimension?.findIndex((dim) => dim.columnId === columnId);
      if (index >= 0) {
        const same = dims.filter((dim) => dim.id === columnId);
        // 之前已经push 过几个了。
        if (same.length > 0) {
          // 并非第一个
          let i = 0;
          let len = same.length;
          let finalIndex = 0;
          while (dimension && dimension.length > i && len >= 0) {
            if (dimension[i].columnId === columnId) {
              len--;
              finalIndex = i;
              i++;
            } else {
              i++;
            }
          }
          return finalIndex;
        } else {
          //第一个
          return index;
        }
      }
      const measureIndex = measure?.findIndex(
        (dim) => dim.columnId === columnId,
      );
      if (measureIndex >= 0) {
        const same = dims.filter((dim) => dim.id === columnId);

        if (same.length > 0) {
          // 并非第一个
          let i = 0;
          let len = same.length;
          let finalIndex = 0;
          while (measure && measure.length > i && len >= 0) {
            if (measure[i].columnId === columnId) {
              len--;
              finalIndex = i;
              i++;
            } else {
              i++;
            }
          }
          return finalIndex;
        } else {
          return measureIndex;
        }
      }
      return -1;
    };

    const dims: ColumnsType<unknown> = [];
    console.log(dimensionInfo, 'dimensionInfodimension[index]-----1111', data);

    dimensionInfo?.forEach((dim, index) => {
      const dimKey = getSuffix(
        dim.title,
        dim.key,
        getIndex(dim.columnId, dims),
        dataSource[0],
        dim?.fieldType === 'measureInfo' ? 'M' : 'D',
      );
      console.log(
        dimension,
        'dimensionInfodimension[index]-----2222',
        measure,
        dim,
      );
      dims.push({
        title: isMobile() ? (
          <Tooltip title={dim.title} overlayStyle={{ zIndex: 5 }}>
            {dim.title}
          </Tooltip>
        ) : (
          <Tooltip title={findFieldAfterMerge(dimension, measure, dim.key)}>
            {dim.title}
          </Tooltip>
        ),
        dataIndex: dimKey,
        index,
        key: dimKey,
        sortOrder:
          index === sort.index - 1
            ? String(sort.sortOrder) === SortType.ASC
              ? 'ascend'
              : 'descend'
            : undefined,
        width: calculateColumnWidth(dim.title),
        id: dim.columnId,
        fieldType: dim.fieldType,
        ellipsis: false, // 关闭省略号，让内容完全显示
        onCell: () => ({
          style: { whiteSpace: 'nowrap' },
        }),
        render(record) {
          const text = formatValue(record, dim);
          return isMobile() ? (
            <Tooltip title={text} overlayStyle={{ zIndex: 5 }}>
              {text}
            </Tooltip>
          ) : (
            text
          );
        },
      });
    });
    return {
      columns: dims, // 列信息描述
      dataSource, // 数据信息
      total: data?.totalSize,
      pageNum: data?.pageNo,
      pageSize: data?.pageSize,
    };
  };
